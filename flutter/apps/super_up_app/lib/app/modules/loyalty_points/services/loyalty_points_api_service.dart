import 'package:super_up_core/super_up_core.dart';

class LoyaltyPointsApiService {
  Future<int> getUserLoyaltyPoints() async {
    try {
      final response = await VHttpHelper.get(
        url: '/api/v1/loyalty-points/',
      );
      
      if (response.statusCode == 200) {
        final data = response.data['data'];
        return data['loyaltyPoints'] ?? 0;
      } else {
        throw Exception('Failed to fetch loyalty points');
      }
    } catch (e) {
      throw Exception('Error fetching loyalty points: $e');
    }
  }
}
