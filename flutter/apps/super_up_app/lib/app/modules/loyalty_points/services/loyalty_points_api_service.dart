import 'package:get_it/get_it.dart';
import '../../../core/api_service/profile/profile_api_service.dart';

class LoyaltyPointsApiService {
  final ProfileApiService _profileApiService = GetIt.I.get<ProfileApiService>();

  Future<int> getUserLoyaltyPoints() async {
    try {
      final response = await _profileApiService.getUserLoyaltyPoints();

      if (response.statusCode == 200) {
        final data = response.body['data'];
        return data['loyaltyPoints'] ?? 0;
      } else {
        throw Exception('Failed to fetch loyalty points');
      }
    } catch (e) {
      throw Exception('Error fetching loyalty points: $e');
    }
  }
}
