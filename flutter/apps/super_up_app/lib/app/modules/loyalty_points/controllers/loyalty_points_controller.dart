import 'package:super_up_core/super_up_core.dart';
import '../services/loyalty_points_api_service.dart';

class LoyaltyPointsController extends ValueNotifier<SLoadingState<int>> {
  final LoyaltyPointsApiService _apiService = LoyaltyPointsApiService();

  LoyaltyPointsController() : super(SLoadingState.loading());

  void onInit() {
    getLoyaltyPoints();
  }

  void onClose() {
    dispose();
  }

  Future<void> getLoyaltyPoints() async {
    try {
      value = SLoadingState.loading();
      final points = await _apiService.getUserLoyaltyPoints();
      value = SLoadingState.success(points);
    } catch (e) {
      value = SLoadingState.error(e.toString());
    }
  }

  void update() {
    notifyListeners();
  }
}
